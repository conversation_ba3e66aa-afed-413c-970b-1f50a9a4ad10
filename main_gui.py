import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import os
import glob
from difflib import SequenceMatcher
from typing import Dict, List, Tuple, Optional
import threading
import datetime
import json
import openpyxl
from openpyxl.styles import Font
import re
import concurrent.futures
from pypinyin import lazy_pinyin, Style

TRIAL_DAYS = 7
LICENSE_FILE = "license_info.dat"

def check_trial():
    today = datetime.date.today()
    if not os.path.exists(LICENSE_FILE):
        # 首次运行，写入日期
        with open(LICENSE_FILE, "w") as f:
            json.dump({"first_run": today.isoformat()}, f)
        return True
    else:
        with open(LICENSE_FILE, "r") as f:
            data = json.load(f)
        first_run = datetime.date.fromisoformat(data["first_run"])
        days_used = (today - first_run).days
        if days_used < TRIAL_DAYS:
            return True
        else:
            return False

class MedicalRecordsGUI:
    def __init__(self):
        if not check_trial():
            messagebox.showwarning("试用期到期", "试用期已结束，请联系作者获取正式授权！")
            exit()
        
        self.root = tk.Tk()
        self.root.title("河南省漯河市弘济医院医疗诊疗信息管理系统")
        self.root.geometry("1200x700")  # 稍微增加宽度以适应新布局
        self.root.resizable(True, True)
        
        # 初始化数据
        self.manager = None
        self.excel_folder_path = ""
        self.current_search_results = {}  # 存储当前搜索结果的详细数据
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 初始化状态
        self.update_status("请选择Excel文件夹并加载数据")
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        # 定义全局字体
        self.default_font = ('SimHei', 18)  # 这里设置全局字体和大小
        style.configure('.', font=self.default_font)
        style.configure('Title.TLabel', font=('SimHei', 16, 'bold'))
        style.configure('Header.TLabel', font=('SimHei', 12, 'bold'))
        style.configure('Status.TLabel', foreground='blue')
        style.configure('Treeview', font=('SimHei', 14))  # 或更小
        style.configure('Treeview.Heading', font=('SimHei', 14, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="弘济医院医疗诊疗信息管理系统", style='Title.TLabel')
        title_label.pack()
        
        # 创建主要区域
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 左侧区域 - 添加新病例
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        # 右侧区域 - 文件管理和搜索
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # 创建各个功能区域
        self.create_add_record_section(left_frame)  # 添加新病例放到左边
        self.create_file_section(right_frame)       # 文件管理放到右边
        self.create_search_section(right_frame)     # 搜索功能放到右边
        self.create_results_section(right_frame)    # 结果显示放到右边
        self.create_status_section(self.root)
    
    def create_file_section(self, parent):
        """创建文件管理区域"""
        file_frame = ttk.LabelFrame(parent, text="文件管理", padding=10)
        file_frame.pack(fill='x', pady=(0, 10))
        
        # 文件夹选择
        folder_frame = ttk.Frame(file_frame)
        folder_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Label(folder_frame, text="文件夹(Excel/CSV):").pack(side='left')
        
        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, state='readonly')
        self.folder_entry.pack(side='left', fill='x', expand=True, padx=(5, 5))
        
        ttk.Button(folder_frame, text="选择文件夹", command=self.select_folder).pack(side='right')
        
        # 加载按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill='x')
        
        self.load_button = ttk.Button(button_frame, text="加载文件(Excel/CSV)", command=self.load_files)
        self.load_button.pack(side='left')
        
        # 注释掉生成测试数据按钮
        # ttk.Button(button_frame, text="生成测试数据", command=self.generate_test_data).pack(side='left', padx=(10, 0))
        
        # 文件信息显示
        self.file_info_var = tk.StringVar(value="未加载文件")
        ttk.Label(file_frame, textvariable=self.file_info_var, style='Status.TLabel').pack(anchor='w', pady=(5, 0))
    
    def create_search_section(self, parent):
        """创建搜索区域"""
        search_frame = ttk.LabelFrame(parent, text="查找相似病例", padding=10)
        search_frame.pack(fill='x', pady=(0, 10))
        
        # 姓名输入
        ttk.Label(search_frame, text="姓名:").grid(row=0, column=0, sticky='w', pady=2)
        self.search_name_var = tk.StringVar()
        name_entry = ttk.Entry(search_frame, textvariable=self.search_name_var, width=40)
        name_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # 性别输入
        ttk.Label(search_frame, text="性别:").grid(row=0, column=2, sticky='w', pady=2, padx=(10, 0))
        self.search_gender_var = tk.StringVar()
        gender_entry = ttk.Entry(search_frame, textvariable=self.search_gender_var, width=20)
        gender_entry.grid(row=0, column=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 住址输入
        ttk.Label(search_frame, text="住址:").grid(row=1, column=0, sticky='w', pady=2)
        self.search_address_var = tk.StringVar()
        address_entry = ttk.Entry(search_frame, textvariable=self.search_address_var, width=40)
        address_entry.grid(row=1, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 症状输入
        ttk.Label(search_frame, text="症状:").grid(row=2, column=0, sticky='w', pady=2)
        self.symptoms_var = tk.StringVar()
        symptoms_entry = ttk.Entry(search_frame, textvariable=self.symptoms_var, width=40)
        symptoms_entry.grid(row=2, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 病情输入
        ttk.Label(search_frame, text="病情:").grid(row=3, column=0, sticky='w', pady=2)
        self.condition_var = tk.StringVar()
        condition_entry = ttk.Entry(search_frame, textvariable=self.condition_var, width=40)
        condition_entry.grid(row=3, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 搜索按钮
        search_button = ttk.Button(search_frame, text="搜索病例", command=self.search_cases)
        search_button.grid(row=4, column=0, columnspan=4, pady=(10, 0))
        
        # 配置网格权重
        search_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(3, weight=1)
        
        # 绑定回车键
        name_entry.bind('<Return>', lambda e: self.search_cases())
        gender_entry.bind('<Return>', lambda e: self.search_cases())
        address_entry.bind('<Return>', lambda e: self.search_cases())
        symptoms_entry.bind('<Return>', lambda e: self.search_cases())
        condition_entry.bind('<Return>', lambda e: self.search_cases())
    
    def create_results_section(self, parent):
        """创建结果显示区域"""
        results_frame = ttk.LabelFrame(parent, text="搜索结果", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        # 创建Treeview显示结果
        columns = ('就诊时间', '姓名', '年龄', '性别', '住址', '症状病情', '处方', '用法用量')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        column_widths = {'就诊时间': 180, '姓名': 80, '年龄': 50, '性别': 40, '住址': 120, '症状病情': 200, '处方': 200, '用法用量': 150}
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        results_scrollbar_y = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        results_scrollbar_x = ttk.Scrollbar(results_frame, orient='horizontal', command=self.results_tree.xview)
        
        self.results_tree.configure(yscrollcommand=results_scrollbar_y.set, xscrollcommand=results_scrollbar_x.set)
        
        # 布局
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        results_scrollbar_y.grid(row=0, column=1, sticky='ns')
        results_scrollbar_x.grid(row=1, column=0, sticky='ew')
        
        # 配置网格权重
        results_frame.rowconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)
        
        # 双击查看详情
        self.results_tree.bind('<Double-1>', self.show_case_details)
    
    def create_add_record_section(self, parent):
        """创建添加新病例区域"""
        add_frame = ttk.LabelFrame(parent, text="添加新病例", padding=10)
        add_frame.pack(fill='both', expand=True)
        
        # 输入字段
        fields = [
            ('姓名:', 'name_var'),
            ('年龄:', 'age_var'),
            ('性别:', 'gender_var'),
            ('住址:', 'address_var'),
            ('联系方式:', 'contact_var'),
            ('就诊时间:', 'visit_time_var'),
            ('症状病情:', 'symptoms_add_var'),
            ('处方:', 'prescription_var'),
            ('用法用量:', 'usage_var'),
        ]
        
        self.add_vars = {}
        self.add_record_entries = {}  # 新增：保存Entry/Text控件引用
        
        for i, (label, var_name) in enumerate(fields):
            ttk.Label(add_frame, text=label).grid(row=i, column=0, sticky='nw', pady=2)  # 改为nw对齐

            if var_name in ['symptoms_add_var', 'prescription_var', 'usage_var']:
                self.add_vars[var_name] = tk.StringVar()
                if var_name == 'prescription_var':
                    entry = tk.Text(add_frame, height=10, width=50, wrap='word', font=self.default_font)
                elif var_name == 'usage_var':
                    entry = tk.Text(add_frame, height=3, width=45, wrap='word', font=self.default_font)
                else:
                    entry = tk.Text(add_frame, height=5, width=45, wrap='word', font=self.default_font)
                entry.grid(row=i, column=1, sticky='ew', padx=(5, 0), pady=2)
                setattr(self, f'{var_name}_widget', entry)
                self.add_record_entries[var_name] = entry
            elif var_name == 'visit_time_var':
                # 特殊处理就诊时间：年月日分离输入
                time_frame = ttk.Frame(add_frame)
                time_frame.grid(row=i, column=1, sticky='ew', padx=(5, 0), pady=2)

                # 年输入框
                self.year_var = tk.StringVar()
                year_entry = ttk.Entry(time_frame, textvariable=self.year_var, width=6)
                year_entry.pack(side='left')
                ttk.Label(time_frame, text="年").pack(side='left', padx=(2, 5))

                # 月输入框
                self.month_var = tk.StringVar()
                month_entry = ttk.Entry(time_frame, textvariable=self.month_var, width=4)
                month_entry.pack(side='left')
                ttk.Label(time_frame, text="月").pack(side='left', padx=(2, 5))

                # 日输入框
                self.day_var = tk.StringVar()
                day_entry = ttk.Entry(time_frame, textvariable=self.day_var, width=4)
                day_entry.pack(side='left')
                ttk.Label(time_frame, text="日").pack(side='left', padx=(2, 0))

                # 设置默认值为当前日期
                now = datetime.datetime.now()
                self.year_var.set(str(now.year))
                self.month_var.set(str(now.month))
                self.day_var.set(str(now.day))

                # 保存引用
                self.add_record_entries[var_name] = time_frame
                self.add_vars[var_name] = None  # 占位符，实际使用年月日变量
            else:
                self.add_vars[var_name] = tk.StringVar()
                entry = ttk.Entry(add_frame, textvariable=self.add_vars[var_name], width=30)
                entry.grid(row=i, column=1, sticky='ew', padx=(5, 0), pady=2)
                self.add_record_entries[var_name] = entry
        
        # 按钮组
        button_frame = ttk.Frame(add_frame)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=(20, 0), sticky='ew')
        
        # 添加按钮
        add_button = ttk.Button(button_frame, text="添加病例", command=self.add_record)
        add_button.pack(side='top', fill='x', pady=(0, 5))
        
        # 清空按钮
        clear_button = ttk.Button(button_frame, text="清空输入", command=self.clear_add_form)
        clear_button.pack(side='top', fill='x')
        
        # 配置网格权重
        add_frame.columnconfigure(1, weight=1)
        
        # 回车跳转逻辑
        # 依次：姓名->年龄->性别->住址->联系方式->症状病情->处方->用法用量
        self.add_record_entries['name_var'].bind('<Return>', lambda e: self.add_record_entries['age_var'].focus_set())
        self.add_record_entries['age_var'].bind('<Return>', lambda e: self.add_record_entries['gender_var'].focus_set())
        self.add_record_entries['gender_var'].bind('<Return>', lambda e: self.add_record_entries['address_var'].focus_set())
        self.add_record_entries['address_var'].bind('<Return>', lambda e: self.add_record_entries['contact_var'].focus_set())
        self.add_record_entries['contact_var'].bind('<Return>', lambda e: self.add_record_entries['symptoms_add_var'].focus_set())
        # Text控件回车默认换行，这里让回车跳到下一个Entry并阻止换行
        def symptoms_return(event):
            self.add_record_entries['prescription_var'].focus_set()
            return 'break'
        self.add_record_entries['symptoms_add_var'].bind('<Return>', symptoms_return)

        def prescription_return(event):
            self.add_record_entries['usage_var'].focus_set()
            return 'break'
        self.add_record_entries['prescription_var'].bind('<Return>', prescription_return)
        # 用法用量输入框回车不跳转，保留默认行为
    
    def create_status_section(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill='x', side='bottom', padx=10, pady=(0, 5))
        
        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief='sunken', anchor='w')
        status_label.pack(fill='x')
    
    def select_folder(self):
        """选择Excel文件夹"""
        folder = filedialog.askdirectory(title="选择包含Excel文件的文件夹")
        if folder:
            self.excel_folder_path = folder
            self.folder_var.set(folder)
            self.update_status(f"已选择文件夹: {folder}")
    
    def load_files(self):
        """加载Excel和CSV文件（带进度条）"""
        if not self.excel_folder_path:
            messagebox.showwarning("警告", "请先选择文件夹!")
            return
        
        # 创建进度窗口
        self.progress_window = self.create_progress_window()
        self.load_button.config(state='disabled')
        self.is_loading_cancelled = False  # 取消标志
        
        def load_thread():
            try:
                self.manager = MedicalRecordsManager(self.excel_folder_path)
                
                # 设置进度回调
                def progress_callback(current, total, message):
                    if self.is_loading_cancelled:
                        return False  # 取消加载
                    
                    progress = (current / total * 100) if total > 0 else 0
                    self.root.after(0, lambda: self.update_progress(progress, message))
                    return True  # 继续加载
                
                self.manager.set_progress_callback(progress_callback)
                success = self.manager.load_excel_files_with_progress()
                
                if success and not self.is_loading_cancelled:
                    file_count = len([f for f in os.listdir(self.excel_folder_path)
                                    if f.endswith(('.xlsx', '.xls', '.csv'))])
                    record_count = len(self.manager.all_records)
                    
                    self.root.after(0, lambda: self.file_info_var.set(
                        f"已加载 {file_count} 个文件（Excel/CSV），共 {record_count} 条记录"
                    ))
                    self.root.after(0, lambda: self.update_status("文件加载完成"))
                    
                    # 显示提取错误信息（如果有）
                    if self.manager.extraction_errors:
                        self.root.after(0, lambda: self.show_extraction_errors())
                elif self.is_loading_cancelled:
                    self.root.after(0, lambda: self.update_status("加载已取消"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("错误", "加载文件失败!"))
                    self.root.after(0, lambda: self.update_status("加载失败"))
                    
            except Exception as e:
                if not self.is_loading_cancelled:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"加载文件时出错: {str(e)}"))
                    self.root.after(0, lambda: self.update_status("加载出错"))
            finally:
                self.root.after(0, lambda: self.close_progress_window())
                self.root.after(0, lambda: self.load_button.config(state='normal'))
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def create_progress_window(self):
        """创建进度显示窗口"""
        progress_window = tk.Toplevel(self.root)
        progress_window.title("加载进度")
        progress_window.geometry("500x200")
        progress_window.resizable(False, False)
        
        # 居中显示
        progress_window.transient(self.root)
        progress_window.grab_set()
        
        # 禁用关闭按钮
        progress_window.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 主框架
        main_frame = ttk.Frame(progress_window, padding=20)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="正在加载文件...", font=('SimHei', 14, 'bold'))
        title_label.pack(pady=(0, 15))
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(main_frame, variable=progress_var, maximum=100, length=400)
        progress_bar.pack(pady=(0, 10))
        
        # 进度文本
        progress_text_var = tk.StringVar(value="准备开始...")
        progress_label = ttk.Label(main_frame, textvariable=progress_text_var)
        progress_label.pack(pady=(0, 15))
        
        # 详细状态
        status_var = tk.StringVar(value="")
        status_label = ttk.Label(main_frame, textvariable=status_var, font=('SimHei', 10))
        status_label.pack(pady=(0, 15))
        
        # 取消按钮
        def cancel_loading():
            self.is_loading_cancelled = True
            cancel_button.config(state='disabled', text="正在取消...")
        
        cancel_button = ttk.Button(main_frame, text="取消", command=cancel_loading)
        cancel_button.pack()
        
        # 保存引用
        progress_window.progress_var = progress_var
        progress_window.progress_text_var = progress_text_var
        progress_window.status_var = status_var
        progress_window.cancel_button = cancel_button
        
        return progress_window
    
    def update_progress(self, progress, message):
        """更新进度条"""
        if hasattr(self, 'progress_window') and self.progress_window:
            self.progress_window.progress_var.set(progress)
            self.progress_window.progress_text_var.set(f"进度: {progress:.1f}%")
            self.progress_window.status_var.set(message)
    
    def close_progress_window(self):
        """关闭进度窗口"""
        if hasattr(self, 'progress_window') and self.progress_window:
            self.progress_window.destroy()
            self.progress_window = None
    
    def show_extraction_errors(self):
        """显示数据提取过程中的错误信息"""
        if not self.manager.extraction_errors:
            return
            
        # 创建错误信息窗口
        error_window = tk.Toplevel(self.root)
        error_window.title("数据提取警告")
        error_window.geometry("800x600")
        error_window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(error_window)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="数据提取过程中遇到的问题：", font=('SimHei', 14, 'bold'))
        title_label.pack(anchor='w', pady=(0, 10))
        
        # 创建滚动文本框显示错误信息
        text_widget = scrolledtext.ScrolledText(main_frame, wrap='word', font=('SimHei', 12))
        text_widget.pack(fill='both', expand=True, pady=(0, 10))
        
        # 插入错误信息
        error_text = "\n".join([f"• {error}" for error in self.manager.extraction_errors])
        text_widget.insert('1.0', error_text)
        text_widget.config(state='disabled')
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x')
        
        # 关闭按钮
        close_button = ttk.Button(button_frame, text="确定", command=error_window.destroy)
        close_button.pack(side='right')
        
        # 提示信息
        info_label = ttk.Label(button_frame, text="注意：系统已成功加载有效数据，上述问题不影响正常使用。", 
                              foreground='blue', font=('SimHei', 10))
        info_label.pack(side='left')
    
    def search_cases(self):
        """搜索相似病例"""
        if not self.manager:
            messagebox.showwarning("警告", "请先加载Excel文件!")
            return
        
        name = self.search_name_var.get().strip()
        gender = self.search_gender_var.get().strip()
        address = self.search_address_var.get().strip()
        symptoms = self.symptoms_var.get().strip()
        condition = self.condition_var.get().strip()
        
        if not (name or gender or address or symptoms or condition):
            messagebox.showwarning("警告", "请至少输入一个搜索条件!")
            return

        # 清空之前的结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.current_search_results.clear()
        self.update_status("正在搜索相似病例...")

        try:
            similar_cases = self.manager.search_similar_cases(name, gender, address, symptoms, condition)
            
            if similar_cases:
                for i, (case, similarity) in enumerate(similar_cases):
                    # 生成唯一ID
                    result_id = f"result_{i}"
                    
                    # 存储完整的病例数据
                    self.current_search_results[result_id] = case

                    # 合并症状和病情
                    symptoms = case.get('symptoms', '').strip()
                    condition = case.get('condition', '').strip()

                    # 合并症状和病情，避免重复显示相同内容
                    if symptoms and condition and symptoms == condition:
                        # 如果症状和病情内容相同，只显示一次，不加前缀
                        symptoms_condition_text = symptoms
                    else:
                        # 如果内容不同，合并显示，不加"症状："前缀
                        symptoms_condition_parts = []
                        if symptoms:
                            symptoms_condition_parts.append(symptoms)
                        if condition and condition != symptoms:
                            symptoms_condition_parts.append(condition)
                        symptoms_condition_text = ' | '.join(symptoms_condition_parts) if symptoms_condition_parts else '未知'

                    # 限制文本长度以适应显示
                    if len(symptoms_condition_text) > 50:
                        symptoms_condition_display = symptoms_condition_text[:50] + '...'
                    else:
                        symptoms_condition_display = symptoms_condition_text

                    # 处理处方信息
                    prescription = case.get('prescription', '').strip()
                    prescription_display = prescription[:50] + '...' if len(prescription) > 50 else prescription
                    if not prescription_display:
                        prescription_display = '未知'

                    # 处理用法用量
                    usage = case.get('usage', '').strip()
                    usage_display = usage[:30] + '...' if len(usage) > 30 else usage
                    if not usage_display:
                        usage_display = '未知'

                    # 处理住址
                    address_text = case.get('address', '未知')[:25] + '...' if len(case.get('address', '')) > 25 else case.get('address', '未知')

                    # 插入到Treeview，使用result_id作为item的ID
                    self.results_tree.insert('', 'end', iid=result_id, values=(
                        self.format_visit_time(case.get('visit_time', '未知')),
                        case.get('name', '未知'),
                        case.get('age', '未知'),
                        case.get('gender', '未知'),
                        address_text,
                        symptoms_condition_display,
                        prescription_display,
                        usage_display
                    ))
                
                self.update_status(f"找到 {len(similar_cases)} 个相似病例")
            else:
                self.update_status("没有找到相似的病例")
                messagebox.showinfo("提示", "没有找到相似的病例")
                
        except Exception as e:
            messagebox.showerror("错误", f"搜索时出错: {str(e)}")
            self.update_status("搜索出错")
    
    def format_contact(self, contact):
        """格式化联系方式，移除.0后缀"""
        if not contact or contact == '未知':
            return '未知'

        contact_str = str(contact)
        # 如果是浮点数格式（如13603853128.0），转换为整数字符串
        if contact_str.endswith('.0'):
            try:
                return str(int(float(contact_str)))
            except (ValueError, TypeError):
                return contact_str
        return contact_str

    def format_visit_time(self, visit_time):
        """格式化就诊时间，将2025-07-09格式转换为2025.7.9格式"""
        if not visit_time or visit_time == '未知':
            return '未知'

        visit_time_str = str(visit_time).strip()

        # 处理 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式
        if '-' in visit_time_str:
            try:
                # 分离日期和时间部分
                if ' ' in visit_time_str:
                    date_part, time_part = visit_time_str.split(' ', 1)
                else:
                    date_part = visit_time_str
                    time_part = None

                # 处理日期部分：YYYY-MM-DD -> YYYY.M.D
                date_parts = date_part.split('-')
                if len(date_parts) == 3:
                    year, month, day = date_parts
                    # 去掉前导零
                    month = str(int(month))
                    day = str(int(day))
                    formatted_date = f"{year}.{month}.{day}"

                    # 如果有时间部分，保留时间
                    if time_part:
                        return f"{formatted_date} {time_part}"
                    else:
                        return formatted_date
            except (ValueError, IndexError):
                pass

        return visit_time_str

    def setup_text_selection_copy(self, text_widget, parent_window):
        """为文本控件设置选中文字复制功能"""
        # 创建浮动复制按钮（初始隐藏）
        copy_popup = tk.Toplevel(parent_window)
        copy_popup.withdraw()  # 初始隐藏
        copy_popup.overrideredirect(True)  # 无边框窗口
        copy_popup.attributes('-topmost', True)  # 置顶

        copy_btn = tk.Button(copy_popup, text="复制", font=('Arial', 14, 'bold'),
                           bg='lightblue', fg='black', relief='raised', bd=2,
                           width=6, height=1, cursor='hand2',
                           padx=8, pady=4,  # 按钮内文字的内边距
                           command=lambda: self.copy_selected_text(text_widget, copy_popup))
        copy_btn.pack(padx=6, pady=6)  # 按钮外部的间距

        def on_text_select(event):
            """当文本被选中时显示复制按钮"""
            try:
                # 检查是否有选中的文本
                if text_widget.tag_ranges(tk.SEL):
                    # 获取鼠标位置，并增加更大的偏移量避免遮挡
                    x = text_widget.winfo_rootx() + event.x + 50  # 增加水平偏移
                    y = text_widget.winfo_rooty() + event.y - 50  # 增加垂直偏移

                    # 确保按钮不会超出屏幕边界
                    screen_width = text_widget.winfo_screenwidth()
                    screen_height = text_widget.winfo_screenheight()

                    # 如果按钮会超出右边界，则显示在左侧
                    if x + 100 > screen_width:
                        x = text_widget.winfo_rootx() + event.x - 100

                    # 如果按钮会超出上边界，则显示在下方
                    if y < 0:
                        y = text_widget.winfo_rooty() + event.y + 30

                    # 显示复制按钮
                    copy_popup.geometry(f"+{x}+{y}")
                    copy_popup.deiconify()
                else:
                    copy_popup.withdraw()
            except tk.TclError:
                copy_popup.withdraw()

        def on_click_elsewhere(event):
            """点击其他地方时隐藏复制按钮"""
            copy_popup.withdraw()

        # 绑定事件
        text_widget.bind('<ButtonRelease-1>', on_text_select)
        text_widget.bind('<B1-Motion>', on_text_select)
        parent_window.bind('<Button-1>', on_click_elsewhere)

        # 当详情窗口关闭时，也关闭复制按钮窗口
        def on_detail_close():
            copy_popup.destroy()
            parent_window.destroy()

        parent_window.protocol("WM_DELETE_WINDOW", on_detail_close)

    def copy_selected_text(self, text_widget, copy_popup):
        """复制选中的文本到剪贴板"""
        try:
            selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
            if selected_text:
                text_widget.clipboard_clear()
                text_widget.clipboard_append(selected_text)

                # 短暂显示复制成功提示
                copy_popup.withdraw()

                # 创建临时提示
                tip = tk.Toplevel(copy_popup.master)
                tip.overrideredirect(True)
                tip.attributes('-topmost', True)
                tip.configure(bg='green')

                tip_label = tk.Label(tip, text="已复制!", bg='green', fg='white',
                                   font=('Arial', 10, 'bold'))
                tip_label.pack(padx=8, pady=4)

                # 获取复制按钮的位置来显示提示
                x = copy_popup.winfo_x()
                y = copy_popup.winfo_y() - 25
                tip.geometry(f"+{x}+{y}")

                # 1秒后自动关闭提示
                tip.after(1000, tip.destroy)

        except tk.TclError:
            copy_popup.withdraw()

    def show_case_details(self, event):
        """显示病例详情"""
        selection = self.results_tree.selection()
        if not selection:
            return

        # 获取选中项的ID
        item_id = selection[0]

        # 从存储的数据中获取完整病例信息
        if item_id not in self.current_search_results:
            messagebox.showerror("错误", "无法获取病例详细信息")
            return

        case = self.current_search_results[item_id]
        
        # 创建详情窗口
        detail_window = tk.Toplevel(self.root)
        detail_window.title("病例详情")
        detail_window.geometry("700x850")  # 稍微增加高度以适应按钮
        detail_window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(detail_window)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建滚动文本框
        text_widget = scrolledtext.ScrolledText(main_frame, wrap='word', padx=10, pady=10)
        text_widget.pack(fill='both', expand=True, pady=(0, 10))
        text_widget.configure(font=('SimHei', 16))  
        
        # 显示详细信息
        # 合并症状和病情显示（避免重复内容）
        symptoms = case.get('symptoms', '').strip()
        condition = case.get('condition', '').strip()

        # 如果症状和病情内容相同，只显示一次
        if symptoms and condition and symptoms == condition:
            symptoms_condition_text = symptoms
        else:
            # 如果内容不同，分别显示
            symptoms_condition_parts = []
            if symptoms:
                symptoms_condition_parts.append(f"症状：{symptoms}")
            if condition and condition != symptoms:
                symptoms_condition_parts.append(f"病情：{condition}")
            symptoms_condition_text = '\n'.join(symptoms_condition_parts) if symptoms_condition_parts else '未知'

        details = f"""病例详细信息

姓名: {case.get('name', '未知')}
年龄: {case.get('age', '未知')}
性别: {case.get('gender', '未知')}
住址: {case.get('address', '未知')}
联系方式: {self.format_contact(case.get('contact', '未知'))}

症状病情:
{symptoms_condition_text}

处方:
{case.get('prescription', '未知')}

用法用量:
{case.get('usage', '未知')}

就诊时间: {self.format_visit_time(case.get('visit_time', '未知'))}

来源文件: {case.get('source_file', '未知')}
"""
        
        text_widget.insert('1.0', details)
        text_widget.config(state='disabled')

        # 添加选中文字复制功能
        self.setup_text_selection_copy(text_widget, detail_window)

        # 添加按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(5, 0))

        # 添加"复制信息"按钮
        copy_button = ttk.Button(button_frame, text="复制信息",
                                command=lambda: self.copy_case_to_add_form(case, detail_window))
        copy_button.pack(side='left', padx=(0, 10))

        # 添加"关闭"按钮
        close_button = ttk.Button(button_frame, text="关闭",
                                 command=detail_window.destroy)
        close_button.pack(side='right')
    
    def copy_case_to_add_form(self, case, detail_window):
        """将病例信息复制到添加新病例窗口"""
        try:
            # 复制基本信息到Entry控件
            self.add_vars['name_var'].set(case.get('name', ''))
            self.add_vars['age_var'].set(case.get('age', ''))
            self.add_vars['gender_var'].set(case.get('gender', ''))
            self.add_vars['address_var'].set(case.get('address', ''))
            self.add_vars['contact_var'].set(self.format_contact(case.get('contact', '')))
            # 处理就诊时间，解析并设置到年月日输入框
            visit_time = case.get('visit_time', '')
            if visit_time and visit_time != '未知':
                # 尝试解析时间格式
                visit_time_str = str(visit_time).strip()
                if '.' in visit_time_str:
                    # 处理 YYYY.M.D 格式
                    parts = visit_time_str.split('.')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(parts[1])
                        self.day_var.set(parts[2])
                elif '-' in visit_time_str:
                    # 处理 YYYY-MM-DD 格式
                    date_part = visit_time_str.split(' ')[0]  # 去掉时间部分
                    parts = date_part.split('-')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(str(int(parts[1])))  # 去掉前导零
                        self.day_var.set(str(int(parts[2])))    # 去掉前导零

            # 复制症状病情到Text控件（合并症状和病情，避免重复）
            self.symptoms_add_var_widget.delete('1.0', 'end')
            symptoms = case.get('symptoms', '').strip()
            condition = case.get('condition', '').strip()

            # 如果症状和病情内容相同，只显示一次
            if symptoms and condition and symptoms == condition:
                symptoms_condition_text = symptoms
            else:
                # 如果内容不同，分别显示
                symptoms_condition_parts = []
                if symptoms:
                    symptoms_condition_parts.append(f"症状：{symptoms}")
                if condition and condition != symptoms:
                    symptoms_condition_parts.append(f"病情：{condition}")
                symptoms_condition_text = '\n'.join(symptoms_condition_parts)

            if symptoms_condition_text:
                self.symptoms_add_var_widget.insert('1.0', symptoms_condition_text)

            # 复制处方到Text控件
            self.prescription_var_widget.delete('1.0', 'end')
            prescription = case.get('prescription', '')
            if prescription:
                self.prescription_var_widget.insert('1.0', prescription)

            # 复制用法用量到Text控件
            self.usage_var_widget.delete('1.0', 'end')
            usage = case.get('usage', '')
            if usage:
                self.usage_var_widget.insert('1.0', usage)

            # 显示成功消息
            messagebox.showinfo("成功", "病例信息已复制到添加新病例窗口！")

            # 关闭详情窗口
            detail_window.destroy()

            # 将焦点设置到姓名输入框
            self.add_record_entries['name_var'].focus_set()

        except Exception as e:
            messagebox.showerror("错误", f"复制信息时出错: {str(e)}")
    
    def add_record(self):
        """添加新病例"""
        if not self.manager:
            messagebox.showwarning("警告", "请先加载Excel文件!")
            return
        
        # 获取输入数据
        name = self.add_vars['name_var'].get().strip()
        age = self.add_vars['age_var'].get().strip()
        gender = self.add_vars['gender_var'].get().strip()
        address = self.add_vars['address_var'].get().strip()
        contact = self.add_vars['contact_var'].get().strip()
        # 从Text widget获取多行文本
        symptoms = self.symptoms_add_var_widget.get('1.0', 'end-1c').strip()
        prescription = self.prescription_var_widget.get('1.0', 'end-1c').strip()
        usage = self.usage_var_widget.get('1.0', 'end-1c').strip()

        # 获取用户输入的就诊时间（从年月日输入框）
        year = self.year_var.get().strip()
        month = self.month_var.get().strip()
        day = self.day_var.get().strip()

        # 构建就诊时间字符串
        if year and month and day:
            visit_time = f"{year}.{month}.{day}"
        else:
            # 如果用户没有完整输入就诊时间，使用当前日期（新格式）
            now = datetime.datetime.now()
            visit_time = f"{now.year}.{now.month}.{now.day}"

        # 验证必填字段
        if not all([name, symptoms, prescription]):
            messagebox.showwarning("警告", "请填写姓名、症状病情和处方!")
            return

        try:
            # 传递visit_time和用法用量
            success = self.manager.add_new_record(name, age, gender, address, contact, symptoms, "", prescription, usage, visit_time)
            if success:
                messagebox.showinfo("成功", "新病例添加成功!")
                self.clear_add_form()
                self.update_status("新病例添加成功")
                
                # 更新文件信息
                record_count = len(self.manager.all_records)
                file_count = len([f for f in os.listdir(self.excel_folder_path)
                                if f.endswith(('.xlsx', '.xls', '.csv'))])
                self.file_info_var.set(f"已加载 {file_count} 个文件（Excel/CSV），共 {record_count} 条记录")
            else:
                messagebox.showerror("错误", "添加失败!")
        except Exception as e:
            messagebox.showerror("错误", f"添加病例时出错: {str(e)}")
    
    def clear_add_form(self):
        """清空添加表单"""
        for var in self.add_vars.values():
            if var:  # 检查var不为None（visit_time_var为None）
                var.set("")

        self.symptoms_add_var_widget.delete('1.0', 'end')
        self.prescription_var_widget.delete('1.0', 'end')
        self.usage_var_widget.delete('1.0', 'end')

        # 重置年月日输入框为当前日期
        now = datetime.datetime.now()
        self.year_var.set(str(now.year))
        self.month_var.set(str(now.month))
        self.day_var.set(str(now.day))
    
    def generate_test_data(self):
        """生成测试数据"""
        self.update_status("正在生成测试数据...")
        
        def generate_thread():
            try:
                self.create_test_data_internal()
                self.root.after(0, lambda: messagebox.showinfo("成功", "测试数据生成完成!"))
                self.root.after(0, lambda: self.update_status("测试数据生成完成"))
                
                # 自动设置文件夹路径
                if not self.excel_folder_path:
                    records_path = os.path.join(os.getcwd(), "records")
                    if os.path.exists(records_path):
                        self.excel_folder_path = records_path
                        self.root.after(0, lambda: self.folder_var.set(records_path))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"生成测试数据时出错: {str(e)}"))
                self.root.after(0, lambda: self.update_status("生成测试数据失败"))
        
        threading.Thread(target=generate_thread, daemon=True).start()
    
    def create_test_data_internal(self):
        """内部测试数据生成方法"""
        # 模拟病例数据
        test_data = [
            # 感冒相关病例
            {
                '姓名': '张三',
                '年龄': '28',
                '联系方式': '13800138001',
                '症状': '发热、咳嗽、流鼻涕、头痛',
                '病情': '普通感冒',
                '处方': '感冒灵颗粒 3次/日，布洛芬 发热时服用，多休息多喝水'
            },
            {
                '姓名': '李四',
                '年龄': '35',
                '联系方式': '13900139002',
                '症状': '咳嗽、发烧、鼻塞、喉咙痛',
                '病情': '上呼吸道感染',
                '处方': '阿莫西林 500mg 3次/日，复方甘草片 3次/日，多喝温水'
            },
            {
                '姓名': '王五',
                '年龄': '42',
                '联系方式': '13700137003',
                '症状': '流鼻涕、打喷嚏、轻微发热',
                '病情': '病毒性感冒',
                '处方': '维C银翘片 3次/日，扑热息痛 发热时服用，注意保暖'
            },
            
            # 胃病相关病例
            {
                '姓名': '赵六',
                '年龄': '45',
                '联系方式': '13600136004',
                '症状': '胃痛、恶心、腹胀、食欲不振',
                '病情': '慢性胃炎',
                '处方': '奥美拉唑 20mg 2次/日，胃复安 3次/日，饮食清淡规律'
            },
            {
                '姓名': '钱七',
                '年龄': '38',
                '联系方式': '13500135005',
                '症状': '上腹疼痛、烧心、反酸',
                '病情': '胃溃疡',
                '处方': '雷贝拉唑 20mg 2次/日，铝镁加混悬液 4次/日，避免辛辣食物'
            },
            {
                '姓名': '孙八',
                '年龄': '52',
                '联系方式': '13400134006',
                '症状': '胃部不适、饭后腹胀、嗳气',
                '病情': '功能性消化不良',
                '处方': '吗丁啉 3次/日，健胃消食片 3次/日，少食多餐'
            },
            
            # 高血压相关病例
            {
                '姓名': '周九',
                '年龄': '58',
                '联系方式': '13300133007',
                '症状': '头晕、头痛、心悸、疲劳',
                '病情': '高血压',
                '处方': '氨氯地平 5mg 1次/日，定期监测血压，低盐饮食'
            },
            {
                '姓名': '吴十',
                '年龄': '63',
                '联系方式': '13200132008',
                '症状': '头昏、胸闷、心慌',
                '病情': '高血压病',
                '处方': '依那普利 10mg 2次/日，阿司匹林 100mg 1次/日，适量运动'
            },
            
            # 糖尿病相关病例
            {
                '姓名': '郑十一',
                '年龄': '55',
                '联系方式': '13100131009',
                '症状': '多饮、多尿、多食、体重下降',
                '病情': '2型糖尿病',
                '处方': '二甲双胍 500mg 3次/日，定期监测血糖，控制饮食'
            },
            {
                '姓名': '陈十二',
                '年龄': '49',
                '联系方式': '13000130010',
                '症状': '口渴、尿频、视力模糊',
                '病情': '糖尿病',
                '处方': '格列齐特 80mg 2次/日，定期复查，运动疗法'
            }
        ]
        
        # 创建records文件夹
        os.makedirs("records", exist_ok=True)
        
        # 生成Excel文件
        df1 = pd.DataFrame(test_data[:6])
        df1.to_excel("records/内科病例.xlsx", index=False)
        
        df2 = pd.DataFrame(test_data[6:])
        df2.to_excel("records/专科病例.xlsx", index=False)
        
        # 生成不同格式的测试文件
        different_format_data = [
            {
                '患者姓名': '测试病人一',
                '年纪': '30',
                '电话号码': '13111131019',
                '主要症状': '头痛、发热、乏力',
                '疾病诊断': '病毒性感冒',
                '药物治疗': '对乙酰氨基酚 500mg 3次/日，多休息'
            },
            {
                '患者姓名': '测试病人二',
                '年纪': '25',
                '电话号码': '13000130020',
                '主要症状': '咳嗽、胸闷、低热',
                '疾病诊断': '支气管炎',
                '药物治疗': '阿奇霉素 250mg 1次/日，止咳糖浆 3次/日'
            }
        ]
        
        df3 = pd.DataFrame(different_format_data)
        df3.to_excel("records/格式测试.xlsx", index=False)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
    
    def run(self):
        """运行GUI应用"""
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 如果有图标文件
            pass
        except:
            pass
        
        # 启动主循环
        self.root.mainloop()


class MedicalRecordsManager:
    def __init__(self, excel_folder_path: str = "records"):
        """
        医疗记录管理器
        
        Args:
            excel_folder_path: 存放Excel文件的文件夹路径
        """
        self.excel_folder_path = excel_folder_path
        self.all_records = []
        self.column_mappings = {}
        self.extraction_errors = []  # 存储提取过程中的错误
        self.progress_callback = None  # 进度回调函数
        
        # 定义列名映射规则（保留原有格式支持）
        self.column_keywords = {
            'name': ['姓名', '患者', '病人', '名字', '患者姓名', '病人姓名'],
            'age': ['年龄', '岁', '年纪', '岁数'],
            'gender': ['性别', '男女', '性'],
            'address': ['住址', '地址', '家庭住址', '联系地址', '现住址'],
            'contact': ['电话', '手机', '联系方式', '联系电话', '手机号', '电话号码', '家长电话'],
            'symptoms': ['症状', '主诉', '表现', '症状表现', '临床表现', '主要症状', '症状诊断'],
            'condition': ['病情', '诊断', '疾病', '病症', '诊断结果', '病情诊断', '疾病诊断'],
            'prescription': ['处方', '药方', '治疗', '用药', '治疗方案', '药物', '药物治疗'],
            'usage': ['用法用量', '用法', '用量', '服用方法', '服法', '煎服', '代煎'],
            'visit_time': ['就诊时间', '时间', '日期', '挂号时间', '诊疗时间']
        }
    
    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def call_progress_callback(self, current, total, message):
        """调用进度回调"""
        if self.progress_callback:
            return self.progress_callback(current, total, message)
        return True
    
    def load_excel_files_with_progress(self) -> bool:
        """带进度显示的Excel和CSV文件加载"""
        if not os.path.exists(self.excel_folder_path):
            return False

        # 扫描Excel和CSV文件
        excel_files = glob.glob(os.path.join(self.excel_folder_path, "*.xlsx")) + \
                     glob.glob(os.path.join(self.excel_folder_path, "*.xls"))
        csv_files = glob.glob(os.path.join(self.excel_folder_path, "*.csv"))

        all_files = excel_files + csv_files

        if not all_files:
            return False
        
        self.extraction_errors.clear()
        total_files = len(all_files)

        # 报告开始
        if not self.call_progress_callback(0, total_files, "开始加载文件..."):
            return False

        for i, file_path in enumerate(all_files):
            filename = os.path.basename(file_path)

            # 更新进度
            if not self.call_progress_callback(i, total_files, f"正在处理: {filename}"):
                return False  # 用户取消

            try:
                # 根据文件扩展名选择处理方式
                if file_path.lower().endswith('.csv'):
                    # 处理CSV文件
                    self.load_csv_file(file_path, filename)
                else:
                    # 处理Excel文件
                    # 首先尝试标准格式
                    if self.try_load_standard_format(file_path):
                        continue

                    # 然后尝试新的特殊格式（优化版本）
                    self.load_medical_card_format_optimized(file_path, filename)

            except Exception as e:
                error_msg = f"加载文件 {filename} 时出错: {str(e)}"
                self.extraction_errors.append(error_msg)
                continue
        
        # 报告完成
        self.call_progress_callback(total_files, total_files, "加载完成!")
        return len(self.all_records) > 0

    def load_csv_file(self, csv_file: str, filename: str):
        """加载CSV文件"""
        try:
            # 尝试不同的编码格式读取CSV文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if df is None:
                error_msg = f"无法读取CSV文件 {filename}：编码格式不支持"
                self.extraction_errors.append(error_msg)
                return

            if df.empty:
                error_msg = f"CSV文件 {filename} 为空"
                self.extraction_errors.append(error_msg)
                return

            columns = df.columns.tolist()

            # 自动识别列映射
            mapping = self.find_column_mapping(columns)

            # 检查是否找到了基本的必需字段
            required_fields = ['name']  # CSV文件至少需要姓名字段
            found_fields = [field for field in required_fields if field in mapping]

            if len(found_fields) < len(required_fields):
                error_msg = f"CSV文件 {filename} 缺少必需的字段（姓名）"
                self.extraction_errors.append(error_msg)
                return

            # 处理数据
            records_added = 0
            for _, row in df.iterrows():
                record = {}
                for field, col_name in mapping.items():
                    if col_name in df.columns:
                        value = row[col_name]
                        # 处理NaN值
                        if pd.isna(value):
                            record[field] = ""
                        else:
                            record[field] = str(value).strip()

                # 只添加有姓名的记录
                if record.get('name'):
                    record['source_file'] = filename
                    self.all_records.append(record)
                    records_added += 1

            if records_added == 0:
                error_msg = f"CSV文件 {filename} 中没有找到有效的病例记录"
                self.extraction_errors.append(error_msg)

        except Exception as e:
            error_msg = f"处理CSV文件 {filename} 时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
    
    def load_medical_card_format_optimized(self, excel_file: str, filename: str):
        """优化的医疗卡片格式加载（并行处理多sheet）"""
        try:
            # 使用只读模式提高性能
            workbook = openpyxl.load_workbook(excel_file, read_only=True, data_only=True)
            sheet_names = workbook.sheetnames
            
            if not sheet_names:
                return
            
            # 更新进度
            self.call_progress_callback(0, len(sheet_names), f"{filename}: 准备处理 {len(sheet_names)} 个工作表")
            
            # 如果sheet数量较少，使用串行处理
            if len(sheet_names) <= 3:
                self.process_sheets_sequential(workbook, sheet_names, excel_file, filename)
            else:
                # 如果sheet数量较多，使用并行处理
                self.process_sheets_parallel(excel_file, sheet_names, filename)
            
            workbook.close()
            
        except Exception as e:
            error_msg = f"打开文件 {filename} 时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
    
    def process_sheets_sequential(self, workbook, sheet_names, excel_file, filename):
        """串行处理sheet（适用于sheet数量较少的情况）"""
        for i, sheet_name in enumerate(sheet_names):
            try:
                sheet = workbook[sheet_name]
                
                # 跳过空的sheet
                if sheet.max_row <= 1:
                    continue
                
                # 更新进度
                self.call_progress_callback(i, len(sheet_names), 
                                          f"{filename}: 处理工作表 '{sheet_name}'")
                
                patient_records = self.extract_patient_records_from_sheet(sheet, sheet_name, excel_file)
                
                if patient_records:
                    self.all_records.extend(patient_records)
                    
            except Exception as e:
                error_msg = f"处理文件 {filename} 的工作表 '{sheet_name}' 时出错: {str(e)}"
                self.extraction_errors.append(error_msg)
    
    def process_sheets_parallel(self, excel_file, sheet_names, filename):
        """并行处理sheet（适用于sheet数量较多的情况）"""
        def process_single_sheet(sheet_name):
            """处理单个sheet的函数"""
            try:
                # 在新线程中重新打开文件（openpyxl对象不能跨线程）
                workbook = openpyxl.load_workbook(excel_file, read_only=True, data_only=True)
                sheet = workbook[sheet_name]
                
                # 跳过空的sheet
                if sheet.max_row <= 1:
                    workbook.close()
                    return []
                
                records = self.extract_patient_records_from_sheet(sheet, sheet_name, excel_file)
                workbook.close()
                return records
                
            except Exception as e:
                error_msg = f"并行处理文件 {filename} 的工作表 '{sheet_name}' 时出错: {str(e)}"
                self.extraction_errors.append(error_msg)
                return []
        
        # 使用线程池并行处理
        max_workers = min(4, len(sheet_names))  # 限制线程数
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_sheet = {
                executor.submit(process_single_sheet, sheet_name): sheet_name 
                for sheet_name in sheet_names
            }
            
            # 收集结果
            completed_count = 0
            for future in concurrent.futures.as_completed(future_to_sheet):
                sheet_name = future_to_sheet[future]
                completed_count += 1
                
                # 更新进度
                self.call_progress_callback(completed_count, len(sheet_names), 
                                          f"{filename}: 已完成 {completed_count}/{len(sheet_names)} 个工作表")
                
                try:
                    records = future.result(timeout=60)  # 设置超时
                    if records:
                        self.all_records.extend(records)
                except Exception as e:
                    error_msg = f"获取工作表 '{sheet_name}' 结果时出错: {str(e)}"
                    self.extraction_errors.append(error_msg)
    
    def find_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """自动识别Excel列名对应的字段"""
        mapping = {}
        
        for field, keywords in self.column_keywords.items():
            best_match = ""
            best_score = 0
            
            for col in columns:
                for keyword in keywords:
                    # 检查是否包含关键词
                    if keyword in col:
                        score = len(keyword) / len(col)
                        if score > best_score:
                            best_score = score
                            best_match = col
                    
                    # 使用相似度匹配
                    similarity = SequenceMatcher(None, keyword, col).ratio()
                    if similarity > 0.6 and similarity > best_score:
                        best_score = similarity
                        best_match = col
            
            if best_match:
                mapping[field] = best_match
        
        return mapping
    
    def try_load_standard_format(self, excel_file: str) -> bool:
        """尝试加载标准表格格式的Excel文件"""
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            
            if df.empty:
                return False
            
            columns = df.columns.tolist()
            
            # 自动识别列映射
            mapping = self.find_column_mapping(columns)
            
            # 如果没有找到足够的映射关系，说明不是标准格式
            # 至少需要姓名字段，症状或处方字段中的一个
            required_fields = ['name']
            optional_fields = ['symptoms', 'condition', 'prescription']

            found_required = [field for field in required_fields if field in mapping]
            found_optional = [field for field in optional_fields if field in mapping]

            if len(found_required) < len(required_fields) or len(found_optional) == 0:
                return False
            
            # 保存映射关系
            self.column_mappings[excel_file] = mapping
            
            # 处理数据
            for _, row in df.iterrows():
                record = {}
                for field, col_name in mapping.items():
                    if col_name in df.columns:
                        record[field] = str(row[col_name]) if pd.notna(row[col_name]) else ""
                
                if record:  # 只添加非空记录
                    record['source_file'] = os.path.basename(excel_file)
                    self.all_records.append(record)
            
            return True
            
        except Exception as e:
            return False
    
    def load_medical_card_format(self, excel_file: str):
        """加载医疗卡片格式的Excel文件（支持多sheet）"""
        try:
            # 使用openpyxl打开文件以获取所有sheet
            workbook = openpyxl.load_workbook(excel_file)
            
            for sheet_name in workbook.sheetnames:
                try:
                    sheet = workbook[sheet_name]
                    patient_records = self.extract_patient_records_from_sheet(sheet, sheet_name, excel_file)
                    
                    if patient_records:
                        self.all_records.extend(patient_records)
                    else:
                        # 记录没有找到数据的sheet
                        error_msg = f"文件 {os.path.basename(excel_file)} 的工作表 '{sheet_name}' 中未找到有效数据"
                        self.extraction_errors.append(error_msg)
                        
                except Exception as e:
                    error_msg = f"处理文件 {os.path.basename(excel_file)} 的工作表 '{sheet_name}' 时出错: {str(e)}"
                    self.extraction_errors.append(error_msg)
                    
        except Exception as e:
            error_msg = f"打开文件 {os.path.basename(excel_file)} 时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
    
    def extract_patient_records_from_sheet(self, sheet, sheet_name: str, excel_file: str) -> List[Dict]:
        """从工作表中提取患者记录"""
        records = []
        
        try:
            # 提取基本信息（第一行）
            basic_info = self.extract_basic_info(sheet)
            
            if not basic_info.get('name'):
                return []  # 如果没有姓名，跳过这个sheet
            
            # 查找所有就诊记录
            visit_records = self.extract_visit_records(sheet)
            
            # 为每个就诊记录创建完整的病例记录
            for visit in visit_records:
                record = basic_info.copy()
                record.update(visit)
                record['source_file'] = f"{os.path.basename(excel_file)}#{sheet_name}"
                records.append(record)
                
        except Exception as e:
            error_msg = f"提取工作表 '{sheet_name}' 数据时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return records
    
    def extract_basic_info(self, sheet) -> Dict:
        """提取基本信息（姓名、性别、年龄、住址、联系方式）"""
        basic_info = {}
        
        try:
            # 按照固定位置提取基本信息
            # A1: 姓名, B1: 性别, C1: 年龄, D1: 住址, E1: 联系方式
            basic_info['name'] = str(sheet.cell(1, 1).value or '').strip()
            basic_info['gender'] = str(sheet.cell(1, 2).value or '').strip()
            basic_info['age'] = str(sheet.cell(1, 3).value or '').strip()
            basic_info['address'] = str(sheet.cell(1, 4).value or '').strip()
            basic_info['contact'] = str(sheet.cell(1, 5).value or '').strip()
            
            # 如果住址列是空的，可能在其他位置
            if not basic_info['address']:
                # 尝试在其他可能的位置查找
                for col in range(1, 10):
                    cell_value = str(sheet.cell(1, col).value or '').strip()
                    if any(keyword in cell_value for keyword in ['路', '街', '区', '县', '市', '镇', '村', '号']):
                        basic_info['address'] = cell_value
                        break
            
        except Exception as e:
            error_msg = f"提取基本信息时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return basic_info
    
    def extract_visit_records(self, sheet) -> List[Dict]:
        """提取就诊记录"""
        visit_records = []
        
        try:
            max_row = sheet.max_row
            max_col = sheet.max_column
            
            # 扫描所有单元格，查找日期模式
            for row in range(2, max_row + 1):  # 从第2行开始，第1行是基本信息
                for col in range(1, max_col + 1):
                    cell = sheet.cell(row, col)
                    cell_value = str(cell.value or '').strip()
                    
                    # 检查是否是日期格式
                    if self.is_date_pattern(cell_value):
                        visit_record = self.extract_single_visit_record(sheet, row, col, cell_value)
                        if visit_record:
                            visit_records.append(visit_record)
            
        except Exception as e:
            error_msg = f"提取就诊记录时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return visit_records
    
    def is_date_pattern(self, text: str) -> bool:
        """检查文本是否符合日期模式"""
        if not text:
            return False
        
        # 检查 YYYY.MM.DD 或 YYYY.M.D 格式
        date_pattern = r'^\d{4}\.\d{1,2}\.\d{1,2}$'
        return bool(re.match(date_pattern, text))
    
    def extract_single_visit_record(self, sheet, date_row: int, date_col: int, visit_date: str) -> Dict:
        """提取单次就诊记录"""
        try:
            # 提取症状（日期同行的后续内容）
            symptoms = self.extract_symptoms_from_row(sheet, date_row, date_col)
            
            # 提取病情诊断（在症状下方查找）
            condition = self.extract_condition_below_symptoms(sheet, date_row, date_col)
            
            # 提取处方信息（在病情诊断下方查找）
            prescription_info = self.extract_prescription_info(sheet, date_row, date_col)
            
            # 构建就诊记录
            visit_record = {
                'visit_time': visit_date,
                'symptoms': symptoms,
                'condition': condition,
                'prescription': prescription_info.get('prescription', ''),
                'usage': prescription_info.get('usage', '')
            }
            
            # 只有包含有效信息的记录才返回
            if symptoms or condition or prescription_info.get('prescription'):
                return visit_record
            
        except Exception as e:
            error_msg = f"提取单次就诊记录时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return None
    
    def extract_symptoms_from_row(self, sheet, date_row: int, date_col: int) -> str:
        """从日期所在行提取症状信息"""
        symptoms_parts = []
        
        try:
            max_col = sheet.max_column
            
            # 从日期列的下一列开始提取症状
            for col in range(date_col + 1, max_col + 1):
                cell_value = str(sheet.cell(date_row, col).value or '').strip()
                if cell_value and not self.is_date_pattern(cell_value):
                    symptoms_parts.append(cell_value)
            
        except Exception as e:
            error_msg = f"提取症状时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return ' '.join(symptoms_parts)
    
    def extract_condition_below_symptoms(self, sheet, date_row: int, date_col: int) -> str:
        """提取病情诊断（通常在症状下方）"""
        try:
            max_col = sheet.max_column
            
            # 在日期下方的几行中查找病情诊断
            for row_offset in range(1, 5):  # 查找下方4行
                condition_row = date_row + row_offset
                
                for col in range(1, max_col + 1):
                    cell_value = str(sheet.cell(condition_row, col).value or '').strip()
                    
                    # 检查是否是病情诊断的模式
                    if self.looks_like_condition(cell_value):
                        return cell_value
            
        except Exception as e:
            error_msg = f"提取病情诊断时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return ''
    
    def looks_like_condition(self, text: str) -> bool:
        """判断文本是否像病情诊断"""
        if not text or len(text) < 2:
            return False
        
        # 常见的病情诊断关键词
        condition_keywords = ['不和', '虚', '虚弱', '炎', '病', '症', '证', '热', '寒', '湿', '燥', '瘀']
        
        # 检查是否包含病情关键词
        for keyword in condition_keywords:
            if keyword in text:
                return True
        
        # 检查是否是纯中文且长度适中（2-10个字符）
        if 2 <= len(text) <= 10 and all('\u4e00' <= char <= '\u9fff' for char in text):
            return True
            
        return False
    
    def extract_prescription_info(self, sheet, date_row: int, date_col: int) -> Dict:
        """提取处方信息"""
        prescription_parts = []
        usage_parts = []
        
        try:
            max_row = sheet.max_row
            max_col = sheet.max_column
            
            # 在日期下方查找处方信息
            for row in range(date_row + 1, max_row + 1):
                row_prescription = []
                found_usage = False
                
                for col in range(1, max_col + 1):
                    cell_value = str(sheet.cell(row, col).value or '').strip()
                    
                    if not cell_value:
                        continue
                    
                    # 检查是否是用法用量
                    if self.looks_like_usage(cell_value):
                        usage_parts.append(cell_value)
                        found_usage = True
                    # 检查是否是处方内容
                    elif self.looks_like_prescription(cell_value):
                        row_prescription.append(cell_value)
                    # 如果遇到新的日期，说明处方结束
                    elif self.is_date_pattern(cell_value):
                        break
                
                if row_prescription:
                    prescription_parts.extend(row_prescription)
                
                # 如果找到用法用量，通常处方就结束了
                if found_usage:
                    break
            
        except Exception as e:
            error_msg = f"提取处方信息时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return {
            'prescription': ' '.join(prescription_parts),
            'usage': ' '.join(usage_parts)
        }
    
    def looks_like_usage(self, text: str) -> bool:
        """判断文本是否像用法用量"""
        if not text:
            return False
        
        usage_keywords = ['付', '代煎', '服用', '次', '日', '饭前', '饭后', '分下', '后下']
        
        for keyword in usage_keywords:
            if keyword in text:
                return True
                
        return False
    
    def looks_like_prescription(self, text: str) -> bool:
        """判断文本是否像处方内容"""
        if not text or len(text) < 2:
            return False
        
        # 如果包含数字，可能是药材+用量
        if re.search(r'\d+', text):
            return True
        
        # 常见中药材关键词
        herb_keywords = ['胡', '参', '芪', '归', '芍', '地', '苓', '术', '草', '花', '子', '仁', '皮', '根', '叶', '实']
        
        for keyword in herb_keywords:
            if keyword in text:
                return True
                
        return False
    
    def get_pinyin_initials(self, text: str) -> str:
        """获取文本的拼音首字母"""
        if not text:
            return ""

        # 获取拼音首字母
        initials = []
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 判断是否为中文字符
                pinyin_list = lazy_pinyin(char, style=Style.FIRST_LETTER)
                if pinyin_list:
                    initials.append(pinyin_list[0].lower())
            else:
                # 非中文字符直接添加（如英文、数字等）
                initials.append(char.lower())

        return ''.join(initials)

    def fuzzy_match_pinyin(self, search_text: str, target_text: str) -> bool:
        """拼音首字母模糊匹配"""
        if not search_text or not target_text:
            return False

        search_text = search_text.lower().strip()
        target_text = target_text.strip()

        # 1. 直接包含匹配（原有功能）
        if search_text in target_text:
            return True

        # 2. 拼音首字母匹配
        target_initials = self.get_pinyin_initials(target_text)
        if search_text in target_initials:
            return True

        # 3. 拼音首字母开头匹配
        if target_initials.startswith(search_text):
            return True

        # 4. 分词后的拼音首字母匹配（处理多个词的情况）
        words = re.split(r'[，。、；：\s]+', target_text)
        for word in words:
            if word:
                word_initials = self.get_pinyin_initials(word)
                if search_text in word_initials or word_initials.startswith(search_text):
                    return True

        return False

    def calculate_similarity(self, symptoms1: str, condition1: str,
                           symptoms2: str, condition2: str) -> float:
        """计算两个病例的相似度"""
        # 症状相似度
        symptoms_sim = SequenceMatcher(None, symptoms1.lower(), symptoms2.lower()).ratio()

        # 病情相似度
        condition_sim = SequenceMatcher(None, condition1.lower(), condition2.lower()).ratio()

        # 综合相似度 (症状权重0.6，病情权重0.4)
        total_sim = symptoms_sim * 0.6 + condition_sim * 0.4

        return total_sim
    
    def search_similar_cases(self, name: str, gender: str, address: str, symptoms: str, condition: str, min_similarity: float = 0.3, max_results: int = 10):
        similar_cases = []
        for record in self.all_records:
            # 基本信息匹配检查
            if not self.check_basic_info_match(record, name, gender, address):
                continue
            
            # 症状/病情相似度计算
            record_symptoms = record.get('symptoms', '')
            record_condition = record.get('condition', '')
            
            if symptoms or condition:
                similarity = self.calculate_similarity(
                    symptoms, condition, record_symptoms, record_condition
                )
                if similarity >= min_similarity:
                    similar_cases.append((record, similarity))
            else:
                # 如果只按基本信息查找，similarity设为1
                similar_cases.append((record, 1.0))
        
        # 按相似度排序
        similar_cases.sort(key=lambda x: x[1], reverse=True)
        return similar_cases[:max_results]
    
    def check_basic_info_match(self, record: Dict, name: str, gender: str, address: str) -> bool:
        """检查基本信息是否匹配"""
        # 姓名匹配
        if name:
            record_name = record.get('name', '')
            if name not in record_name:
                return False
        
        # 性别匹配
        if gender:
            record_gender = record.get('gender', '')
            if gender not in record_gender:
                return False
        
        # 住址匹配（支持部分匹配）
        if address:
            record_address = record.get('address', '')
            if address not in record_address:
                return False
        
        return True
    
    def add_new_record(self, name, age, gender, address, contact, symptoms, condition, prescription, usage, visit_time):
        new_record = {
            'name': name,
            'age': age,
            'gender': gender,
            'address': address,
            'contact': contact,
            'symptoms': symptoms,
            'condition': condition,
            'prescription': prescription,
            'usage': usage,
            'visit_time': visit_time,
            'source_file': 'new_record'
        }
        self.all_records.append(new_record)
        self.save_new_record(new_record)
        return True
    
    def save_new_record(self, record: Dict):
        """保存新记录到Excel文件"""
        new_records_file = os.path.join(self.excel_folder_path, "new_records.xlsx")
        
        # 准备数据
        record_data = {
            '姓名': [record.get('name', '')],
            '年龄': [record.get('age', '')],
            '性别': [record.get('gender', '')],
            '住址': [record.get('address', '')],
            '联系方式': [record.get('contact', '')],
            '症状': [record.get('symptoms', '')],
            '病情': [record.get('condition', '')],
            '处方': [record.get('prescription', '')],
            '用法用量': [record.get('usage', '')],
            '就诊时间': [record.get('visit_time', '')]
        }
        
        try:
            if os.path.exists(new_records_file):
                # 如果文件存在，追加数据
                existing_df = pd.read_excel(new_records_file)
                new_df = pd.DataFrame(record_data)
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                # 如果文件不存在，创建新文件
                combined_df = pd.DataFrame(record_data)
            
            combined_df.to_excel(new_records_file, index=False)
            
        except Exception as e:
            raise e


if __name__ == "__main__":
    app = MedicalRecordsGUI()
    app.run()